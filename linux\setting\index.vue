<template>
	<page-container ref="pageContainer" title="面板设置">
		<view class="admin-panel">
			<view class="container">
				<!-- 面板状态卡片 -->
				<view class="card status-card">
					<view class="card__header">
						<text class="card__title text-primary">面板状态</text>
					</view>
					<view class="card__content mt-10">
						<view class="status-grid">
							<view class="status-item">
								<text class="status-item__label text-primary">状态：</text>
								<text class="status-item__value status-badge">运行中</text>
							</view>
							<view class="status-item ml-20">
								<text class="status-item__label text-primary">版本：</text>
								<text class="status-item__value text-secondary">v{{ panelStatusInfo.version }}</text>
							</view>
						</view>
						<view class="status-item">
							<text class="status-item__label text-primary">运行时间：</text>
							<text class="status-item__value text-secondary">{{ panelStatusInfo.time }}</text>
						</view>
						<view class="status-item">
							<text class="status-item__label text-primary">内存使用：</text>
							<text class="status-item__value text-secondary">{{
								`${panelStatusInfo.mem?.memRealUsed ?? 0}MB / ${panelStatusInfo.mem?.memNewTotal ?? 0}`
							}}</text>
						</view>
					</view>
				</view>

				<!-- 禁用PING -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">禁用PING</text>
							</view>
							<view class="toggle-switch">
								<uv-switch
									v-model="disablePing"
									activeColor="var(--primary-color)"
									inactiveColor="#d1d5db"
									size="20"
									@change="originalPingState = true"
								></uv-switch>
							</view>
						</view>
						<view class="setting-item__content">
							<text class="setting-item__hint"> 禁用PING，防止面板被恶意扫描 </text>
						</view>
					</view>
				</view>

				<!-- 开发者模式卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">开发者模式</text>
							</view>
							<view class="toggle-switch">
								<uv-switch
									v-model="developerMode"
									activeColor="var(--primary-color)"
									inactiveColor="#d1d5db"
									size="20"
									@change="originalDeveloperState = true"
								></uv-switch>
							</view>
						</view>
						<view class="setting-item__content">
							<text class="setting-item__hint"> 仅第三方开发者开放接口使用（普通用户请勿开启） </text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 禁PING -->
		<custom-dialog
			v-model="originalPingState"
			:title="disablePing ? '禁PING' : '解禁PING'"
			contentHeight="200rpx"
			@cancel="cancelPing"
			@confirm="confirmPing"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{
					disablePing
						? '禁PING后不影响服务器正常使用，但无法ping通服务器，您确定要禁PING吗？'
						: '解除禁PING状态可能会被黑客发现您的服务器，您确定要解禁吗？'
				}}
			</view>
		</custom-dialog>

		<!-- 开发者模式 -->
		<custom-dialog
			v-model="originalDeveloperState"
			:title="developerMode ? '开启开发者模式' : '关闭开发者模式'"
			contentHeight="100rpx"
			@cancel="cancelDeveloper"
			@confirm="confirmDeveloper"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{
					developerMode
						? '警告！此功能普通用户别开启!\n仅第三方开发者开发使用，普通用户请勿开启；\n请不要在生产环境开启，这可能增加服务器安全风险；\n开启开发者模式可能会占用大量内存；'
						: '您确定要关闭开发者模式吗 ?'
				}}
			</view>
		</custom-dialog>
	</page-container>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import customDialog from '@/components/CustomDialog/index.vue';
	import {
		developerMode,
		getPanelInfo,
		panelStatusInfo,
		pageContainer,
		disablePing,
		originalPingState,
		originalDeveloperState,
		cancelPing,
		cancelDeveloper,
		getFirewallInfo,
		confirmDeveloper,
		confirmPing
	} from './useController.js';

	onMounted(() => {
		getPanelInfo();
		getFirewallInfo();
	});
</script>

<style lang="scss">
	// 变量定义
	$primary-color: var(--primary-color);
	$primary-hover-color: #16a34a;
	$danger-color: #ef4444;
	$success-color: var(--primary-color);
	$text-color: var(--text-color-primary);
	$text-light-color: var(--text-color-secondary);
	$border-color: #e5e7eb;
	$card-bg-color: var(--dialog-bg-color);
	$input-border-color: #d1d5db;
	$input-focus-border-color: var(--primary-color);
	$divider-color: #e5e7eb;

	$border-radius: 12rpx;
	$box-shadow: 0 3rpx 15rpx rgba(0, 0, 0, 0.08);

	// 混合器
	@mixin flex($direction: row, $justify: flex-start, $align: stretch) {
		display: flex;
		flex-direction: $direction;
		justify-content: $justify;
		align-items: $align;
	}

	@mixin transition($property: all, $duration: 0.2s, $timing: ease-in-out) {
		transition: $property $duration $timing;
	}

	.container {
		max-width: 750rpx;
		margin: 0 auto;
		padding: 30rpx 30rpx 0;
	}

	.card {
		background-color: $card-bg-color;
		border-radius: $border-radius;
		box-shadow: $box-shadow;
		overflow: hidden;
		margin-bottom: 24rpx;
		border: 1rpx solid rgba(226, 232, 240, 0.8);

		&__header {
			border-bottom: 1rpx solid $border-color;
		}

		&__title {
			font-size: 32rpx;
			font-weight: 500;
		}

		&__content {
			padding: 0;
		}
	}

	.status-card {
		margin-bottom: 24rpx;
	}

	.status-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 16rpx;
	}

	.status-item {
		@include flex(row, space-between, center);
		padding: 8rpx 0;

		&__label {
			font-size: 28rpx;
			font-weight: 500;
		}

		&__value {
			font-size: 28rpx;
		}
	}

	.status-badge {
		background-color: $success-color;
		color: white;
		padding: 8rpx 16rpx;
		border-radius: 9999rpx;
		font-size: 24rpx;
		font-weight: 500;
	}

	.setting-item {
		&:hover {
			background-color: rgba(0, 0, 0, 0.01);
		}

		&__header {
			@include flex(row, space-between, center);
			margin-bottom: 16rpx;
		}

		&__title {
			@include flex(row, flex-start, center);
		}

		&__label {
			font-size: 28rpx;
			font-weight: 500;
		}

		&__content {
			position: relative;
		}

		&__hint {
			font-size: 24rpx;
			color: $text-light-color;
			margin-top: 8rpx;
		}
	}

	.btn-group {
		@include flex(row, flex-end, center);
	}

	// uvui组件样式调整
	.uv-input {
		margin-bottom: 6rpx;
	}

	.password-input {
		position: relative;
	}

	.toggle-switch {
		position: relative;
		display: inline-block;
	}

	.text-danger {
		color: $danger-color;
		font-weight: 500;
	}

	.text-success {
		color: $success-color;
	}

	// 图标样式
	.icon {
		width: 32rpx;
		height: 32rpx;
		display: inline-block;
		color: $text-light-color;

		&-eye::before {
			content: '👁️';
		}
		&-eye-off::before {
			content: '👁️‍🗨️';
		}
	}
</style>
