import { ref } from 'vue';
import { getServerList } from '@/api/serverList';
import { getPublicConfig, setDebug, serviceAdmin, restartServer } from '@/api/config';
import { getPingInfo, setPing } from '@/api/firewall';
import { isObject } from '@/utils/type';

export const developerMode = ref(false);
export const panelStatusInfo = ref({});
export const safeConfig = ref({});
export const pageContainer = ref(null);
export const disablePing = ref(false);
// 临时保存原始状态
export const originalPingState = ref(false);
export const originalDeveloperState = ref(false);

// 重启服务器相关状态
export const showRestartDialog = ref(false);
export const restartSteps = ref([
	{ name: '停止Web服务', status: 'pending', color: '#666' },
	{ name: '停止MySQL服务', status: 'pending', color: '#666' },
	{ name: '发送重启命令', status: 'pending', color: '#666' },
	{ name: '检测服务器状态', status: 'pending', color: '#666' },
]);
export const currentStepIndex = ref(-1);
export const showRestartButtons = ref(true);

export const handleSave = (setting) => {
	uni.showToast({
		title: `${setting}设置成功`,
		icon: 'success',
	});
};

// 获取面板信息
export const getPanelInfo = async () => {
	try {
		const res = await getServerList();
		panelStatusInfo.value = res;
		const publicConfig = await getPublicConfig();
		developerMode.value = publicConfig.debug === 'checked';
		if (isObject(publicConfig?.panel)) {
			safeConfig.value = publicConfig.panel;
		}
	} catch (error) {
		console.log(error);
	}
};

// 获取防火墙信息
export const getFirewallInfo = async () => {
	try {
		const res = await getPingInfo();
		disablePing.value = !res.ping;
	} catch (error) {
		console.log(error);
	}
};

// 取消禁PING
export const cancelPing = () => {
	originalPingState.value = false;
	setTimeout(() => {
		disablePing.value = !disablePing.value;
	}, 300);
};

// 取消开发者模式
export const cancelDeveloper = () => {
	originalDeveloperState.value = false;
	setTimeout(() => {
		developerMode.value = !developerMode.value;
	}, 300);
};

// 确认开发者模式
export const confirmDeveloper = async (close) => {
	try {
		const res = await setDebug();
		if (res.status) {
			close && close();
		}
		res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
	} catch (error) {
		console.log(error);
	}
};

// 确认禁PING
export const confirmPing = async (close) => {
	try {
		const res = await setPing({ status: disablePing.value ? 0 : 1 });
		if (res.status) {
			close && close();
		}
		res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
	} catch (error) {
		console.log(error);
	}
};

// 显示重启服务器弹窗
export const showRestartServerDialog = () => {
	showRestartDialog.value = true;
	// 重置状态
	currentStepIndex.value = -1;
	showRestartButtons.value = true;
	restartSteps.value.forEach((step) => {
		step.status = 'pending';
		step.color = '#666';
	});
};

// 取消重启服务器
export const cancelRestartServer = () => {
	showRestartDialog.value = false;
};

// 执行重启服务器流程
export const confirmRestartServer = async () => {
	showRestartButtons.value = false;

	try {
		// 步骤1: 停止nginx服务
		currentStepIndex.value = 0;
		restartSteps.value[0].status = 'running';
		restartSteps.value[0].color = '#1890ff';

		const nginxRes = await serviceAdmin({ name: 'nginx', type: 'stop' });
		if (nginxRes.status) {
			restartSteps.value[0].status = 'completed';
			restartSteps.value[0].color = '#20a50a';
		} else {
			throw new Error('停止nginx服务失败: ' + nginxRes.msg);
		}

		// 等待一秒
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// 步骤2: 停止mysqld服务
		currentStepIndex.value = 1;
		restartSteps.value[1].status = 'running';
		restartSteps.value[1].color = '#1890ff';

		const mysqldRes = await serviceAdmin({ name: 'mysqld', type: 'stop' });
		if (mysqldRes.status) {
			restartSteps.value[1].status = 'completed';
			restartSteps.value[1].color = '#20a50a';
		} else {
			throw new Error('停止mysqld服务失败: ' + mysqldRes.msg);
		}

		// 等待一秒
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// 步骤3: 重启服务器
		currentStepIndex.value = 2;
		restartSteps.value[2].status = 'running';
		restartSteps.value[2].color = '#1890ff';

		const restartRes = await restartServer();
		if (restartRes.status) {
			restartSteps.value[2].status = 'completed';
			restartSteps.value[2].color = '#20a50a';

			// 步骤4: 等待服务器重启
			currentStepIndex.value = 3;
			restartSteps.value[3].status = 'running';
			restartSteps.value[3].color = '#1890ff';

			// 显示重启提示
			pageContainer.value.notify.success('服务器重启命令已发送，正在检测服务器状态...');

			// 开始轮询检测服务器状态
			await checkServerStatus();
		} else {
			throw new Error('重启服务器失败: ' + restartRes.msg);
		}
	} catch (error) {
		console.error('重启服务器过程中出错:', error);
		pageContainer.value.notify.error(error.message || '重启服务器失败');

		// 标记当前步骤为失败
		if (currentStepIndex.value >= 0) {
			restartSteps.value[currentStepIndex.value].status = 'failed';
			restartSteps.value[currentStepIndex.value].color = '#ff4d4f';
		}

		// 重新显示按钮
		showRestartButtons.value = true;
	}
};

// 检测服务器状态
const checkServerStatus = async () => {
	let attempts = 0;
	const maxAttempts = 30; // 最多尝试30次，即5分钟

	const pollServer = async () => {
		try {
			attempts++;
			console.log(`第${attempts}次检测服务器状态...`);

			const res = await getServerList();

			// 如果请求成功，说明服务器已经重启完成
			if (res) {
				restartSteps.value[3].status = 'completed';
				restartSteps.value[3].color = '#20a50a';

				pageContainer.value.notify.success('服务器重启完成！');

				// 等待2秒后关闭弹窗
				setTimeout(() => {
					showRestartDialog.value = false;
				}, 2000);

				return;
			}
		} catch (error) {
			console.log(`第${attempts}次检测失败:`, error);
		}

		// 如果还没有达到最大尝试次数，继续轮询
		if (attempts < maxAttempts) {
			setTimeout(pollServer, 10000); // 10秒后再次检测
		} else {
			// 超过最大尝试次数，标记为失败
			restartSteps.value[3].status = 'failed';
			restartSteps.value[3].color = '#ff4d4f';

			pageContainer.value.notify.error('服务器重启超时，请手动检查服务器状态');

			// 重新显示按钮，允许用户重试
			showRestartButtons.value = true;
		}
	};

	// 等待30秒后开始第一次检测（给服务器一些重启时间）
	setTimeout(pollServer, 30000);
};
